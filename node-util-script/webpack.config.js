// import path from 'path';
// export default {
//     entry: "./main.js",
//     mode: "production",
//     output: {
//         path: path.resolve() + '/dist',
//         filename: "main.js",
//         libraryTarget: 'commonjs'
//     },
//     // externals: {
//     //     // "fs": true,
//     //     // "vm": true,
//     // },
//     // resolve: {
//     //     fallback: {
//     //         // "fs": false,
//     //         // "vm": false,
//     //     },
//     // },
//     target: 'node' // 'webworker' or 'node' or 'node-webkit'
// };
const path = require('path')

module.exports = {
    entry: "./main.js",
    mode: "production",
    output: {
        // path: __dirname + '/dist',
        path: path.resolve(__dirname, 'dist'),
        filename: "main.js"
    },
    // externals: {
    //     // "fs": true,
    //     // "vm": true,
    // },
    // resolve: {
    //     fallback: {
    //         // "fs": false,
    //         // "vm": false,
    //     },
    // },
    target: 'node' // 'webworker' or 'node' or 'node-webkit'
};