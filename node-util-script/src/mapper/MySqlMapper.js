const mysql = require('mysql2/promise');

/**
 * MySql数据库映射
 */
export class MySqlMapper {

    constructor(ctx, data) {
        this.ctx = ctx;
        this.data = data;
    }

    /**
     * 查询
     * @param sql {String}
     * @param values {Array}
     * @param opts
     * @returns {Promise<unknown>}
     */
    async query(sql, values, opts = {}) {
        const self = this;
        let conn;
        if (opts.conn) {
            conn = opts.conn;
            const [rows, fields] = await conn.execute(sql, values);
            return rows;
        }
        else {
            conn = await self.getConn();
            const [rows, fields] = await conn.execute(sql, values);
            await conn.end();
            return rows;
        }
    }

    /**
     * 获取数据库连接
     * 使用前：conn.connect()
     * 使用后：conn.end()
     * @returns {Connection}
     */
    async getConn() {
        return await mysql.createConnection({
            host: this.data.conn.host,
            port: this.data.conn.port,
            user: this.data.conn.user,
            password: this.data.conn.password,
            database: this.data.conn.database,
            // connectionLimit: 10,
            // connectTimeout: 10000
        });
    }
}