const fs = require('fs');
const Client = require('ssh2').Client;
import { isLinuxDirByMode } from '../util/linux_file_util';

export class SFtpModel {

    constructor(cfg) {
        this._cfg = cfg;
        this._conn = null;
    }

    log(msg) {
        console.log(msg);
    }
    /**
     * 获取远端目录中文件列表
     * @param {*} dirPath 远端目录路径
     */
    async getRemoteFileListSync(dirPath) {
        const self = this;
        return await new Promise((resolve, reject) => {
            self.getRemoteFileList(dirPath, (fileList) => {
                resolve(fileList);
            });
        });
    }

    getRemoteFileList(dirPath, cb) {
        this.getSftp((sftp, conn) => {
            sftp.readdir(dirPath, (err, list) => {
                if (err) {
                    throw err;
                }
                const fileList = list.map(item => {
                    return {
                        name: item.filename,
                        // 类型：0 目录，1 文件
                        type: isLinuxDirByMode(item.attrs.mode) ? 0 : 1,
                        size: item.attrs.size,
                        // 最后访问时间戳（10位）
                        atime: item.attrs.atime,
                        // 最后修改时间戳（10位）
                        mtime: item.attrs.mtime,
                    };
                });
                if (cb != null) cb(fileList, conn);
            });
        });
    }

    getSftp(cb) {
        const self = this;

        self.getConn((conn) => {
            conn.sftp((err, sftp) => {
                if (err) {
                    self.log(`发生异常 -> ${err}`);
                    self.log(`尝试重新连接...`);
                    // throw err;
                    self._conn = null;
                    self.getSftp(cb);
                }
                else {
                    cb(sftp, conn);
                }
            });
        });
    }

    async getConnSync() {
        const self = this;
        return await new Promise((resolve, reject) => {
            self.getConn((conn) => {
                resolve(conn);
            });
        });
    }

    getConn(cb) {
        const self = this;

        if (this._conn != null) cb(this._conn);
        else {
            self.log('连接 sftp ...');
            const conn = new Client();
            conn.on('ready', () => {
                self._conn = conn;
                cb(self._conn);
            }).connect({
                host: self._cfg.host, //'**************',
                port: self._cfg.port, //2200,
                username: self._cfg.username, //'root@100.68.132.4_by_chengccz_sl',
                password: self._cfg.password, //'Dianxin@12345!!'
            });
        }
    }

}