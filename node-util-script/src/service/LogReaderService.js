import fs from 'fs';
import readline from 'readline';

export class LogReaderService {

    async filterLogToSave(filePath, savePath) {
        const list = await this.filterLog(filePath);
        fs.writeFileSync(savePath, list.join('\n'));
    }

    async filterLog(filePath) {
        const list = [];

        await this._eachFileAllLine(filePath, (line, lineNumber) => {
            // console.log(line);
            if (line.match(/[(]\d+[\/]\d+/) && line.indexOf('删除') !== -1 && line.endsWith('...')) {
                list.push(line);
            }
        })

        return list;
    }

    /**
     * 读取文件每一行，然后回调
     * @param {string} filePath 文件路径
     * @param {function} func 每行回调函数，接收参数：(line, lineNumber)
     * @returns {Promise<void>} 返回Promise，完成时resolve
     * @private
     */
    async _eachFileAllLine(filePath, func) {
        return new Promise((resolve, reject) => {
            // 检查文件是否存在
            if (!fs.existsSync(filePath)) {
                reject(new Error(`文件不存在: ${filePath}`));
                return;
            }

            // 创建可读流
            const fileStream = fs.createReadStream(filePath, { encoding: 'utf8' });

            // 创建readline接口
            const rl = readline.createInterface({
                input: fileStream,
                crlfDelay: Infinity // 处理Windows的\r\n换行符
            });

            let lineNumber = 0;

            // 逐行读取
            rl.on('line', (line) => {
                lineNumber++;
                try {
                    func(line, lineNumber);
                } catch (error) {
                    rl.close();
                    reject(new Error(`处理第${lineNumber}行时出错: ${error.message}`));
                }
            });

            // 读取完成
            rl.on('close', () => {
                resolve();
            });

            // 处理错误
            rl.on('error', (error) => {
                reject(new Error(`读取文件时出错: ${error.message}`));
            });

            fileStream.on('error', (error) => {
                reject(new Error(`打开文件时出错: ${error.message}`));
            });
        });
    }
}