const fs = require('fs');
const Client = require('ssh2').Client;
import { isLinuxDirByMode } from '../util/linux_file_util';


/**
 * 动物园代码发布
 * 安装 npm install ssh2
 */
export default class DwyOACodePublishService {

    constructor() {

    }

    // 上传后台war包
    async uploadBackWarSync() {
        const self = this;
        return await new Promise((resolve, reject) => {
            self.uploadBackWar(() => {
                resolve();
            });
        });
    }

    // 上传政务首页war包
    async uploadInnerIndexWarSync() {
        const self = this;
        return await new Promise((resolve, reject) => {
            self.uploadInnerIndexWar(() => {
                resolve();
            });
        });
    }

    // 上传后台war包
    uploadBackWar(cb) {
        console.log('上传文件 dwy_oa_back.war ...');
        this.uploadFile('D:\\GITWarehouse\\Other\\Business\\dwy-oa\\dwy-oa-back\\target\\dwy_oa_back.war', '/tenv/dwy_oa_back.war', () => {
            console.log('上传文件 dwy_oa_back.war 完毕');
            if (cb != null) cb();
        });
    }

    // 上传政务首页war包
    uploadInnerIndexWar(cb) {
        console.log('上传文件 dwy-oa-inner-index_war.war ...');
        this.uploadFile('D:\\GITWarehouse\\Other\\Business\\dwy-oa\\dwy-oa-inner-index\\target\\dwy-oa-inner-index_war.war', 
        '/tenv/dwy-oa-inner-index_war.war', () => {
            console.log('上传文件 dwy-oa-inner-index_war.war 完毕');
            if (cb != null) cb();
        });
    }

    /**
     * 获取远端目录中文件列表
     * @param {*} dirPath 远端目录路径
     */
    async getRemoteFileListSync(dirPath) {
        const self = this;
        return await new Promise((resolve, reject) => {
            self.getRemoteFileList(dirPath, (fileList) => {
                resolve(fileList);
            });
        });
    }

    /**
     * 获取远端目录中文件列表
     * @param {*} dirPath 远端目录路径
     * @param {*} cb 
     */
    getRemoteFileList(dirPath, cb) {
        this.getZWWWSFtp((sftp, conn) => {
            sftp.readdir(dirPath, (err, list) => {
                if (err) {
                    conn.end();
                    throw err;
                }
                const fileList = list.map(item => {
                    return {
                        name: item.filename,
                        // 类型：0 目录，1 文件
                        type: isLinuxDirByMode(item.attrs.mode) ? 0 : 1,
                        size: item.attrs.size,
                        // 最后访问时间戳（10位）
                        atime: item.attrs.atime,
                        // 最后修改时间戳（10位）
                        mtime: item.attrs.mtime,
                    };
                });
                conn.end();
                if (cb != null) cb(fileList);
            });
        });
    }

    /**
     * 上传文件（同步）
     * @param {*} src 待上传文件路径
     * @param {*} dst 远端保存文件路径
     */
    async uploadFileSync(src, dst) {
        const self = this;
        return await new Promise((resolve, reject) => {
            self.uploadFile(src, dst, () => {
                resolve();
            });
        });
    }

    /**
     * 上传文件（异步）
     * @param {*} src 待上传文件路径
     * @param {*} dst 远端保存文件路径
     * @param {*} cb 
     */
    uploadFile(src, dst, cb) {
        this.getZWWWSFtp((sftp, conn) => {
            sftp.fastPut(src, dst, (err) => {
                if (err) throw err;
                conn.end();
                if (cb != null) cb();
            });
        });
    }

    /**
     * 强行删除指定远端目录及其所有子文件和子目录
     * @param {*} dirPath 远端目录路径
     * @param {*} cb 回调函数
     */
    forceRemoveRemoteDirectory(dirPath, cb) {
        this.getZWWWSFtp((sftp, conn) => {
            conn.exec(`rm -rf ${dirPath}`, (err, stream) => {
                if (err) {
                    conn.end();
                    throw err;
                }
                stream.on('close', (code, signal) => {
                    conn.end();
                    if (cb != null) cb();
                }).on('data', (data) => {
                    console.log('STDOUT: ' + data);
                }).stderr.on('data', (data) => {
                    console.log('STDERR: ' + data);
                });
            });
        });
    }

    // 连接政务外网并获取sftp对象
    getZWWWSFtp(cb) {
        console.log('连接 政务外网 sftp ...');
        const conn = new Client();
        conn.on('ready', () => {

            conn.sftp((err, sftp) => {
                if (err) throw err;
                cb(sftp, conn);
            });
        }).connect({
            host: '**************',
            port: 2200,
            username: 'root@100.68.132.4_by_chengccz_sl',
            password: 'Dianxin@12345!!'
        });
    }
}