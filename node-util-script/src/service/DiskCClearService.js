const fs = require('fs');
/**
 * C盘清理服务
 */
export default class DiskCClearService {
    constructor() {
        this.toDelDirs = [
        ];

        const user_administrator = 'C:\\Users\\<USER>\\AppData\\Local\\Microsoft';
        const edgeDevDir = microsoftDir + '\\Edge Dev';
        const edgeDevUserDataDefaultDir = edgeDevDir + '\\User Data\\Default';
        const edgeDevUserDataProfile1Dir = edgeDevDir + '\\User Data\\Profile 1';
        const edgeDir = microsoftDir + '\\Edge';
        const edgeUserDataDefaultDir = edgeDir + '\\User Data\\Default';
        const edgeUserDataProfile3Dir = edgeDir + '\\User Data\\Profile 3';

        // cache
        this.toDelDirs.push(`${user_administrator}\\.cache`);
        this.toDelDirs.push(`${user_administrator}\\.android`);
        this.toDelDirs.push(`${user_administrator}\\.astropy`);
        this.toDelDirs.push(`${user_administrator}\\.bito`);
        this.toDelDirs.push(`${user_administrator}\\AppData\\Local\\UnrealEngine\\Common`);

        // *** edge dev ***
        this.toDelDirs.push(edgeDevDir + '\\User Data\\Crashpad\\reports');
        // default
        this.toDelDirs.push(edgeDevUserDataDefaultDir + '\\Cache');
        this.toDelDirs.push(edgeDevUserDataDefaultDir + '\\Code Cache');
        this.toDelDirs.push(edgeDevUserDataDefaultDir + '\\IndexedDB');
        this.toDelDirs.push(edgeDevUserDataDefaultDir + '\\Service Worker');
        // profile 1
        this.toDelDirs.push(edgeDevUserDataProfile1Dir + '\\Cache');
        this.toDelDirs.push(edgeDevUserDataProfile1Dir + '\\Code Cache');
        this.toDelDirs.push(edgeDevUserDataProfile1Dir + '\\IndexedDB');
        this.toDelDirs.push(edgeDevUserDataProfile1Dir + '\\Service Worker');

        // *** edge ***
        this.toDelDirs.push(edgeDir + '\\User Data\\Crashpad\\reports');
        // default
        this.toDelDirs.push(edgeUserDataDefaultDir + '\\Cache');
        this.toDelDirs.push(edgeUserDataDefaultDir + '\\Code Cache');
        this.toDelDirs.push(edgeUserDataDefaultDir + '\\IndexedDB');
        this.toDelDirs.push(edgeUserDataDefaultDir + '\\Service Worker');
        // profile 3
        this.toDelDirs.push(edgeUserDataProfile3Dir + '\\Cache');
        this.toDelDirs.push(edgeUserDataProfile3Dir + '\\Code Cache');
        this.toDelDirs.push(edgeUserDataProfile3Dir + '\\IndexedDB');
        this.toDelDirs.push(edgeUserDataProfile3Dir + '\\Service Worker');

        // *** Tencent ***
        this.toDelDirs.push('C:\\Users\\<USER>\\AppData\\Roaming\\Tencent\\QQLive');
        this.toDelDirs.push('C:\\Users\\<USER>\\AppData\\Local\\Tencent');

        // *** Code ***
        this.toDelDirs.push('C:\\Users\\<USER>\\AppData\\Roaming\\Code');

        // *** 其它 ***
        this.toDelDirs.push(user_administrator + '\\AppData\\Local\\Yodao\\DeskDict\\updaters');
        this.toDelDirs.push(user_administrator + '\\AppData\\Local\\ynote-desktop-updater');
        this.toDelDirs.push(user_administrator + '\\AppData\\Local\\cursor-updater');
        this.toDelDirs.push(user_administrator + '\\AppData\\Local\\vortex-updater');
        this.toDelDirs.push(user_administrator + '\\AppData\\Local\\apifox-updater');
    }

    start() {
        console.log('开始清理...');
        for (const i in this.toDelDirs) {
            const toDelDir = this.toDelDirs[i];
            this.delDir(toDelDir);
        }
        console.log('清理完毕！');
    }

    delDir(dirPath) {
        if (fs.existsSync(dirPath)) {
            // 删除目录及其所有子目录和文件
            // fs.rmdirSync(dirPath, { recursive: true });
            fs.rmSync(dirPath, { recursive: true });
        }
    }
}