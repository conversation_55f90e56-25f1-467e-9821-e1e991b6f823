const fs = require('fs');
const path = require('path');

/**
 * 文件/目录查询（文件名、文件内容、目录名）
 */
export default class SearchFileService {
    constructor() {

    }

    /**
     * 查询文件/目录（默认是在目标目录下所有层级中查找）
     * @param {*} kw 关键词，可能是文件名的，也可能是文件内容的，如果是内容中找就只看文本内容的
     * @param {*} dirPath 目标目录路径
     * @param {*} options { matchTxtType: '<0|1>' }
     * matchTxtType 匹配文本类型：0 采样判定、1 预设格式判定
     */
    search(kw, dirPath, options) {
        if (options == null) options = {};

        // 找到后的文件路径列表
        const list = [];

        this._searchInDir(kw, dirPath, list, options);

        return list;
    }

    /**
     * 在目录中查找文件
     * @param {*} kw 
     * @param {*} dirPath 
     * @param {*} options 
     */
    _searchInDir(kw, dirPath, list, options) {
        const self = this;
        if (list == null) list = [];

        this._eachInDirPath(dirPath, (path, isFile) => {
            if (self._isMatch(kw, path, isFile, options)) {
                list.push(path);
            }

            if (!isFile) {
                self._searchInDir(kw, path, list, options);
            }
        }, options);

        return list;
    }

    /**
     * 遍历目标目录下的所有文件和目录（单层就行了）
     * @param {*} dirPath 目标目录路径
     * @param {*} func 遍历回调方法，例：(path, isFile)
     */
    _eachInDirPath(dirPath, func, options) {
        const files = fs.readdirSync(dirPath);
        files.forEach(file => {
            const filePath = path.join(dirPath, file);
            const isFile = fs.statSync(filePath).isFile();
            func(filePath, isFile);
        });
    }

    /**
     * 判定目标（文件或目录）是否匹配
     * 主要：如果要判断内容是否匹配，需要先判断文件是否是文本内容，例如图片，dll等这些肯定不能进行内容匹配
     * @param {*} kw 关键词（文件名，目录名，文件内容）
     * @param {*} path 目标路径
     */
    _isMatch(kw, path, isFile, options) {
        if (isFile) {
            const fileName = path.split('/').pop();
            if (fileName.includes(kw)) {
                return true;
            }

            if (!this._isTxtFile(path, options)) return false;

            const content = fs.readFileSync(path, 'utf-8');
            return content.includes(kw);
        } 
        else {
            const dirName = path.split('/').pop();
            return dirName.includes(kw);
        }
    }

    /**
     * 判定目标文件路径是否是文本文件
     * @param {*} filePath 目标文件路径
     */
    _isTxtFile(filePath, options) {
        // 预设格式判断（速度快）
        if (options.matchTxtType === 1) {
            const ext = filePath.split('.').pop().toLowerCase();
            const textFileExts = ['txt', 'md', 'json', 'html', 'css', 'less', 'scss', 
            'java', 'cs', 'js', 'py', 'vue'];
            if (textFileExts.includes(ext)) {
                return true;
            }
            else {
                return false;
            }
        }
        // 内容采样判断
        else {
            const buffer = Buffer.alloc(512);
            const fd = fs.openSync(filePath, 'r');
            fs.readSync(fd, buffer, 0, 512, 0);
            fs.closeSync(fd);
            const textChars = buffer.toString('utf-8').replace(/[\x00-\x08\x0E-\x1F\x7F-\x9F]/g, '');
            return textChars.length / buffer.length > 0.8;
        }
    }
}