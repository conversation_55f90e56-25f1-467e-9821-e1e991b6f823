import {estimateTokens} from "../util/gpt-util";

/**
 * 评估数据有多少tokens
 */
export default class EstimateTokensService {

    /**
     * 评估数据有多少tokens
     * 每行样例：{"content":"xxx"}
     * @param filePath
     */
    estimeate0(filePath, cb) {
        let tokens = 0;
        this._readFileEachLine(filePath, (line) => {
            tokens += estimateTokens(line);
        }, () => {
            cb(tokens);
        });
    }

    /**
     * 读取文件内容
     * @param filePath 文件路径
     * @return {string} 文件内容
     * @private
     */
    _readFileCon(filePath) {
        return require('fs').readFileSync(filePath, 'utf8');
    }

    /**
     * 读取文件每一行
     * @param filePath 文件路径
     * @param eachCb 每一行回调
     * @private
     */
    _readFileEachLine(filePath, eachCb, allDoneCb) {
        require('fs').createReadStream(filePath).on('data', (chunk) => {
            chunk.toString().split('\n').forEach((line) => {
                eachCb(line);
            });
        }).on('end', () => {
            allDoneCb();
        });
    }
}