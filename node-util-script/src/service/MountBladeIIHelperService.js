/**
 * 骑马与砍杀2 辅助服务
 */
export default class MountBladeIIHelperService {

    exec() {
        console.log('开始执行...');
        const path = `E:\\Games\\Steam\\steamapps\\common\\Mount & Blade II Bannerlord\\Modules\\SandBoxCore\\ModuleData\\items\\weapons.xml`;
        const xmlCon = this._loadFileSync(path);
        const list = this._parseAmmoClassToList(xmlCon);
        const list1 = this._filterDuplicated(list);
        console.log(list1);
    }

    /**
     * 过滤重复项
     * @param {string[]} list 
     * @returns {string[]} 过滤后的列表
     */
    _filterDuplicated(list) {
        return Array.from(new Set(list));
    }

    /**
     * 把内容中所有ammo_class属性值解析出来，并返回一个列表
     * @param {*} content xml文件内容
     * @returns {string[]} 返回ammo_class属性值列表
     */
    _parseAmmoClassToList(content) {
        const ammoClassList = [];
        // const regex = /ammo_class="([^"]+)"/g;
        const regex = /Type="([^"]+)"/g;
        let match;

        while ((match = regex.exec(content)) !== null) {
            ammoClassList.push(match[1]);
        }

        return ammoClassList;
    }

    /**
     * 读取本地文件内容
     * @param {*} filePath 文件路径，例：E:\Games\Steam\steamapps\common\Mount & Blade II Bannerlord\Modules\SandBoxCore\ModuleData\items\weapons.xml
     * @returns {string} 文件内容
     */
    _loadFileSync(filePath) {
        const fs = require('fs');
        try {
            const content = fs.readFileSync(filePath, 'utf8');
            return content;
        } catch (error) {
            console.error(`读取文件失败: ${error.message}`);
            return null;
        }
    }
}