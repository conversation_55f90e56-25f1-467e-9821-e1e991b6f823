import {MySqlMapper} from "../mapper/MySqlMapper";
import fs from 'fs';
import readline from 'readline';

/**
 * 知识库数据修复
 */
export class KbDataRepairService {

    constructor(ctx) {
        this.now_mySqlMapper = new MySqlMapper(ctx, {
            conn: {
                host: 'cs.juzhengdevelop.com',
                port: 5301,
                user: 'root',
                password: 'Agc@12qfk!',
                database: 'csaui6jt-test',
            }
        });
        this.backup_mySqlMapper = new MySqlMapper(ctx, {
            conn: {
                host: '*************',
                port: 5301,
                user: 'root',
                password: 'Agc@12qfk!',
                database: 'csaui6jt-test',
            }
        });
    }

    async repair() {
        let mode = 0;

        if (mode === 0) {
            const deletedInfoTitleList = await this.loadDeleteLog();
            console.log(`共加载 ${deletedInfoTitleList.length} 条删除记录`)

            const notFindList = []; // 未能在备份库中找到的记录
            const findRows = [];

            let curIndex = 1;
            let total = deletedInfoTitleList.length;
            const backUp_conn = await this.backup_mySqlMapper.getConn()
            for (const deletedInfoTitle of deletedInfoTitleList) {
                console.log(`【${curIndex}/${total}】查询 ${deletedInfoTitle} ...`)
                const r = await this.backup_mySqlMapper
                    .query(`select * from s_extension_app_data where sac = 'bot.test' and app_id = 406 and search->'$.name' = ?`, [deletedInfoTitle],
                        { conn: backUp_conn })

                if (r.length === 0) {
                    notFindList.push(deletedInfoTitle);
                }
                else {
                    findRows.push(r[0]);
                }

                curIndex++;
            }
            await backUp_conn.end();
            // console.log(JSON.stringify(deletedInfoTitleList))

            // 保存 findRows
            fs.writeFileSync('./data/kb_find_rows.txt', JSON.stringify(findRows), 'utf8');

            // 生成 insert sql
            const sql = this._createMySqlInsertSql('s_extension_app_data', findRows);
            fs.writeFileSync('./data/kb_insert.sql', sql, 'utf8');

            console.log(`未能在备份库中找到 ${notFindList.length} 条记录`)
            fs.writeFileSync('./data/kb_not_find.txt', notFindList.join('\n'), 'utf8');
            console.log('处理完毕！')
        }
        else if (mode === 1) {
            const findRows = [];
            const str = fs.readFileSync('./data/kb_find_rows.txt', 'utf8');
            JSON.parse(str).forEach(item => {
                findRows.push(item);
            })
        }
    }

    async loadDeleteLog() {
        const list = [];
        await this._eachFileAllLine(`./data/default_deleted.log`, (line) => {
            list.push(line.match(/[(]\d+\/\d+\s删除\s([^.]+)\s[.]/)[1])
        })
        return list;
    }

    /**
     * 读取文件每一行，然后回调
     * @param {string} filePath 文件路径
     * @param {function} func 每行回调函数，接收参数：(line, lineNumber)
     * @returns {Promise<void>} 返回Promise，完成时resolve
     * @private
     */
    async _eachFileAllLine(filePath, func) {
        return new Promise((resolve, reject) => {
            // 检查文件是否存在
            if (!fs.existsSync(filePath)) {
                reject(new Error(`文件不存在: ${filePath}`));
                return;
            }

            // 创建可读流
            const fileStream = fs.createReadStream(filePath, { encoding: 'utf8' });

            // 创建readline接口
            const rl = readline.createInterface({
                input: fileStream,
                crlfDelay: Infinity // 处理Windows的\r\n换行符
            });

            let lineNumber = 0;

            // 逐行读取
            rl.on('line', (line) => {
                lineNumber++;
                try {
                    func(line, lineNumber);
                } catch (error) {
                    rl.close();
                    reject(new Error(`处理第${lineNumber}行时出错: ${error.message}`));
                }
            });

            // 读取完成
            rl.on('close', () => {
                resolve();
            });

            // 处理错误
            rl.on('error', (error) => {
                reject(new Error(`读取文件时出错: ${error.message}`));
            });

            fileStream.on('error', (error) => {
                reject(new Error(`打开文件时出错: ${error.message}`));
            });
        });
    }

    /**
     * 把传入的列表生成MySql的批处理的insert语句
     * create_time 和 change_time 都是TIMESTAMP类型
     * @param tableName {String} 表名
     * @param {Array} list 数据行列表，每个元素是从数据库查询出来的行对象
     * @returns {string} 生成的INSERT SQL语句
     * @private
     */
    _createMySqlInsertSql(tableName, list) {
        if (!list || list.length === 0) {
            return '';
        }

        // 获取第一行数据来确定字段名
        const firstRow = list[0];
        const columns = Object.keys(firstRow);

        // 构建INSERT语句的开头部分
        const columnNames = columns.map(col => `\`${col}\``).join(', ');
        let sql = `INSERT INTO \`${tableName}\` (${columnNames}) VALUES\n`;

        // 构建VALUES部分
        const valueRows = list.map(row => {
            const values = columns.map(col => {
                const value = row[col];
                if (value === null || value === undefined) {
                    return 'NULL';
                } else if (col === 'create_time' || col === 'change_time') {
                    // TIMESTAMP类型字段处理
                    if (value instanceof Date) {
                        // Date对象转换为MySQL TIMESTAMP格式
                        return `'${value.toISOString().slice(0, 19).replace('T', ' ')}'`;
                    } else if (typeof value === 'string') {
                        // 字符串格式的时间戳，直接使用
                        return `'${value}'`;
                    } else if (typeof value === 'number') {
                        // Unix时间戳转换为MySQL TIMESTAMP格式
                        const date = new Date(value);
                        return `'${date.toISOString().slice(0, 19).replace('T', ' ')}'`;
                    } else {
                        return 'NULL';
                    }
                } else if (typeof value === 'string') {
                    // 转义单引号和反斜杠
                    const escapedValue = value.replace(/\\/g, '\\\\').replace(/'/g, "\\'");
                    return `'${escapedValue}'`;
                } else if (typeof value === 'object') {
                    // JSON对象需要转换为字符串
                    const jsonStr = JSON.stringify(value).replace(/\\/g, '\\\\').replace(/'/g, "\\'");
                    return `'${jsonStr}'`;
                } else {
                    // 数字、布尔值等直接返回
                    return value.toString();
                }
            });
            return `(${values.join(', ')})`;
        });

        sql += valueRows.join(',\n');
        sql += ';';

        return sql;
    }
}