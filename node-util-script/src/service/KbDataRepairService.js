import {MySqlMapper} from "../mapper/MySqlMapper";
import fs from 'fs';
import readline from 'readline';

/**
 * 知识库数据修复
 */
export class KbDataRepairService {

    constructor(ctx) {
        this.now_mySqlMapper = new MySqlMapper(ctx, {
            conn: {
                host: 'cs.juzhengdevelop.com',
                port: 5301,
                user: 'root',
                password: 'Agc@12qfk!',
                database: 'csaui6jt-test',
            }
        });
        this.backup_mySqlMapper = new MySqlMapper(ctx, {
            conn: {
                host: '*************',
                port: 5301,
                user: 'root',
                password: 'Agc@12qfk!',
                database: 'csaui6jt-test',
            }
        });
    }

    async repair() {
        const deletedInfoTitleList = await this.loadDeleteLog();
        console.log(`共加载 ${deletedInfoTitleList.length} 条删除记录`)

        const notFindList = []; // 未能在备份库中找到的记录
        const findRows = [];

        const backUp_conn = await this.backup_mySqlMapper.getConn()
        for (const deletedInfoTitle of deletedInfoTitleList) {
            console.log(`查询 ${deletedInfoTitle} ...`)
            const r = await this.backup_mySqlMapper
                .query(`select * from s_extension_app_data where sac = 'bot.test' and app_id = 406 and search->'$.name' = ?`, [deletedInfoTitle],
                    { conn: backUp_conn })

            if (r.length === 0) {
                notFindList.push(deletedInfoTitle);
            }
            else {
                findRows.push(r[0]);
            }
            // console.log(JSON.stringify(r))
        }
        await backUp_conn.end();
        // console.log(JSON.stringify(deletedInfoTitleList))
    }

    async loadDeleteLog() {
        const list = [];
        await this._eachFileAllLine(`./data/default_deleted.log`, (line) => {
            list.push(line.match(/[(]\d+\/\d+\s删除\s([^.]+)\s[.]/)[1])
        })
        return list;
    }

    /**
     * 读取文件每一行，然后回调
     * @param {string} filePath 文件路径
     * @param {function} func 每行回调函数，接收参数：(line, lineNumber)
     * @returns {Promise<void>} 返回Promise，完成时resolve
     * @private
     */
    async _eachFileAllLine(filePath, func) {
        return new Promise((resolve, reject) => {
            // 检查文件是否存在
            if (!fs.existsSync(filePath)) {
                reject(new Error(`文件不存在: ${filePath}`));
                return;
            }

            // 创建可读流
            const fileStream = fs.createReadStream(filePath, { encoding: 'utf8' });

            // 创建readline接口
            const rl = readline.createInterface({
                input: fileStream,
                crlfDelay: Infinity // 处理Windows的\r\n换行符
            });

            let lineNumber = 0;

            // 逐行读取
            rl.on('line', (line) => {
                lineNumber++;
                try {
                    func(line, lineNumber);
                } catch (error) {
                    rl.close();
                    reject(new Error(`处理第${lineNumber}行时出错: ${error.message}`));
                }
            });

            // 读取完成
            rl.on('close', () => {
                resolve();
            });

            // 处理错误
            rl.on('error', (error) => {
                reject(new Error(`读取文件时出错: ${error.message}`));
            });

            fileStream.on('error', (error) => {
                reject(new Error(`打开文件时出错: ${error.message}`));
            });
        });
    }

    /**
     * 把传入的列表生成MySql的批处理的insert语句
     * @param list
     * @returns {string}
     * @private
     */
    _createMySqlInsertSql(list) {

    }
}