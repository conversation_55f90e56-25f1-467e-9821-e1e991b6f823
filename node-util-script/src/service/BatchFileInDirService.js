import fs from 'fs';
import { eachAllFilesInDirPath } from "../util/dir_util"
/**
 * 批量处理目录中文件
 */
export default class BatchFileInDirService {

    constructor() {
        this.tgtDirPath = 'D:\\GITWarehouse\\Other\\Business\\cz_innersys\\cz_innersys_2021_front\\src';
    }

    start() {
        const self = this;
        this.log(`开始批处理文件... ${this.tgtDirPath}`);
        eachAllFilesInDirPath(this.tgtDirPath, (path, isFile) => {

            if (isFile) {
                if (path.endsWith('.scss')) {
                    self.log(`修改文件 ${path}`);
                    fs.renameSync(path, path.replace('.scss', '.less'));
                }
            }
        });
        this.log('处理完毕！');
    }

    log(msg) {
        console.log(msg);
    }
}