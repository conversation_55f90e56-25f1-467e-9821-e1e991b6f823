import fs from 'fs';
import path from 'path';

/**
 * 通过正则表达式来搜索指定目录中的文件
 * @param {string} dirPath 目标目录路径
 * @param {string} expression 正则表达式
 */
export function listFilePathsByRegex(dirPath, expression) {

}

/**
 * 通过通配符来搜索指定目录中的文件
 * @param {string} dirPath 目标目录路径
 * @param {string} expression 通配符表达式
 */
export function listFilePathsByWildcard(dirPath, expression) {
    const fs = require('fs');
    const path = require('path');
    const glob = require('glob');

    let files = glob.sync(path.join(dirPath, expression));
    return files;
}

/**
 * 遍历目标目录中每个文件
 * @param {*} dirPath 目标目录路径
 * @param {*} func 遍历目标文件/目录时的回调函数
 * @param {*} options 
 */
export function eachAllFilesInDirPath(dirPath, func, options) {
    _eachInDirPath(dirPath, func, options);
}

/**
 * 遍历目标目录下的所有文件和目录（单层就行了）
 * @param {*} dirPath 目标目录路径
 * @param {*} func 遍历回调方法，例：(path, isFile)
 */
function _eachInDirPath(dirPath, func, options) {
    const files = fs.readdirSync(dirPath);
    for (const i in files) {
        const file = files[i];
        const filePath = path.join(dirPath, file);
        const isFile = fs.statSync(filePath).isFile();
        let t = func(filePath, isFile);
        if (t === false) {
            return false;
        }
        if (!isFile) {
            t = _eachInDirPath(filePath, func, options);
            if (t === false) {
                return false;
            }
        }
    }
}