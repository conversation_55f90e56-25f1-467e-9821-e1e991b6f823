const fs = require('fs');
const path = require('path');

/**
 * 查询目录下所有层的文件，把文件名包含关键词的文件找出来
 * 排除图片、dll、exe这类二进制文件
 * @param {String} dir 
 * @param {*} keyword 目录/文件名的关键字
 * @returns {[]} 返回找到的文件路径数组
 */
function searchFilesInDirectory(dir, keyword, options = {}) {
    const matchingFiles = [];

    fs.readdirSync(dir, { recursive: true }).forEach(file => {
        let absolute = path.join(dir, file);
        // console.log(absolute);
        let fileExtension = path.extname(absolute);
        let name = path.parse(absolute).name;

        if (fs.statSync(absolute).isDirectory()) return searchFilesInDirectory(absolute, keyword);
        else if (
            fs.statSync(absolute).isFile() 
            && file.indexOf(keyword) !== -1
        // && !['.png', '.jpg', '.jpeg', '.dll', '.exe', '.pyc'].includes(fileExtension) 
        // && fs.readFileSync(absolute, 'utf8').includes(keyword)
        ) {
            if (options.debug) console.log('【' + name + '】路径：' + absolute);
            matchingFiles.push(absolute);
        }
    });

    return matchingFiles;
}

/**
 * 复制包含关键词的文件到新目录
 * @param {String} sourceDir 源目录
 * @param {String} destDir 目标目录
 * @param {*} keyword 关键词
 */
function copyFilesToDirectory(sourceDir, destDir, keyword, options = {}) {
    const matchingFiles = searchFilesInDirectory(sourceDir, keyword);
    if (options.debug) console.log(`共找到 ${matchingFiles.length} 个文件`);

    matchingFiles.forEach(file => {
        // 假设 `file` 是文件的完整路径
        const fileName = path.basename(file); // 从文件路径中提取文件名
        const destFilePath = path.join(destDir, fileName);
        fs.copyFileSync(file, destFilePath);
        if (options.debug) console.log(`复制文件：${file} => ${destFilePath}`);
    });

    if (options.debug) console.log(`复制完毕！`);
}

/**
 * 合并多个文件到一个文件中
 * @param {[string]} filePaths 要被合并的文件列表
 * @param {string} saveFilePath 合并后保存的文件路径
 * @param {*} options 可选项 { splitStr }
 */
export function mergeMultiFilesToOneFile(filePaths, saveFilePath, options = {}) {
    const fs = require('fs');
    const writeStream = fs.createWriteStream(saveFilePath);

    filePaths.forEach((filePath, index) => {
        const data = fs.readFileSync(filePath);
        writeStream.write(data);
        if (options.splitStr && index < filePaths.length - 1) {
            writeStream.write(options.splitStr);
        }
    });

    writeStream.end();
}



module.exports.searchFilesInDirectory = searchFilesInDirectory;
module.exports.copyFilesToDirectory = copyFilesToDirectory;