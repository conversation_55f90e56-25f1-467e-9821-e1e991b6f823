/**
 * 拆分合并的大图
 * 例如：sprite sheet
 * 类似TexturePacker的功能
 * @param {string} filePath 图片路径
 * @param {number} cols 拆分成几列
 * @param {number} rows 拆分成几行
 * @returns {Promise<Array>} 返回拆分后的图片数据数组
 */
export async function splitMergedGraph(filePath, cols, rows) {
    const fs = require('fs').promises;
    const path = require('path');
    const sharp = require('sharp');

    // 参数验证
    if (!filePath || typeof filePath !== 'string') {
        throw new Error('filePath 必须是有效的字符串路径');
    }
    if (!cols || cols <= 0) {
        throw new Error('cols 必须是大于0的数字');
    }
    if (!rows || rows <= 0) {
        throw new Error('rows 必须是大于0的数字');
    }

    try {
        // 读取原始图片
        const imageBuffer = await fs.readFile(filePath);
        const image = sharp(imageBuffer);

        // 获取图片元数据
        const metadata = await image.metadata();
        const { width, height } = metadata;

        console.log(`原图尺寸: ${width}x${height}, 拆分为 ${cols}列 x ${rows}行`);

        // 计算每个小图的尺寸
        const cellWidth = Math.floor(width / cols);
        const cellHeight = Math.floor(height / rows);

        console.log(`每个小图尺寸: ${cellWidth}x${cellHeight}`);

        // 验证计算结果
        if (cellWidth <= 0 || cellHeight <= 0) {
            throw new Error(`计算的小图尺寸无效: ${cellWidth}x${cellHeight}`);
        }

        const results = [];
        const fileName = path.parse(filePath).name;
        const fileDir = path.dirname(filePath);
        const fileExt = path.parse(filePath).ext;

        // 按行列循环拆分图片
        for (let row = 0; row < rows; row++) {
            for (let col = 0; col < cols; col++) {
                // 计算裁剪区域
                const left = col * cellWidth;
                const top = row * cellHeight;

                // 确保裁剪区域不超出图片边界
                const actualWidth = Math.min(cellWidth, width - left);
                const actualHeight = Math.min(cellHeight, height - top);

                console.log(`裁剪区域 [${row},${col}]: left=${left}, top=${top}, width=${actualWidth}, height=${actualHeight}`);

                // 验证裁剪区域
                if (left < 0 || top < 0 || actualWidth <= 0 || actualHeight <= 0 ||
                    left >= width || top >= height) {
                    console.warn(`跳过无效的裁剪区域 [${row},${col}]`);
                    continue;
                }

                // 裁剪图片 - 使用整数值并确保参数正确
                const extractOptions = {
                    left: Math.floor(left),
                    top: Math.floor(top),
                    width: Math.floor(actualWidth),
                    height: Math.floor(actualHeight)
                };

                console.log(`Sharp extract 参数:`, extractOptions);

                const croppedBuffer = await sharp(imageBuffer)
                    .extract(extractOptions)
                    .toBuffer();

                // 生成输出文件名
                const outputFileName = `${fileName}_${row}_${col}${fileExt}`;
                const outputPath = path.join(fileDir, outputFileName);

                // 保存裁剪后的图片
                await fs.writeFile(outputPath, croppedBuffer);

                results.push({
                    row: row,
                    col: col,
                    filePath: outputPath,
                    width: actualWidth,
                    height: actualHeight
                });
            }
        }

        console.log(`成功拆分图片: ${filePath} -> ${results.length} 个小图`);
        return results;

    } catch (error) {
        console.error('拆分图片失败:', error);
        throw error;
    }
}