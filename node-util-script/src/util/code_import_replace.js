const fs = require('fs');
const path = require('path');
const glob = require('glob');

// 项目案例：csjsv6-threejs-prototype

/**
 * 检测src目录中所有代码文件，把其中import中是绝对路径的替换成相对路径
 * 例：import {useEngineRenderTJSObjectPosition} from "@//api/core/game-engine/tjs-object-util"; 变成 import {useEngineRenderTJSObjectPosition} from "../../../core/game-engine/tjs-object-util";
 * 例：import TopObject from "@//api/core/TopObject"; 变成 import TopObject from "../TopObject";
 * 具体如何替换根据文件所在目录结构决定
 * @param {*} srcDirPath src目录路径，例如：@//api/... 就是 <srcDirPath>/api/...
 */
function replaceImportPaths(srcDirPath, lockPathKwList) {
    // 获取src目录下所有的.js文件
    const files = glob.sync(`${srcDirPath}/**/*.js`);

    files.forEach(file => {
        let isNeedCareCode = true;

        if (lockPathKwList != null && lockPathKwList.length > 0) {
            let findItem = lockPathKwList.find((n) => {
                return file.replace(/\\/g, '/').indexOf(n) !== -1;
            });
            if (findItem == null) isNeedCareCode = false;
        }

        if (isNeedCareCode) {
            let content = fs.readFileSync(file, 'utf-8');

            // 使用正则表达式匹配所有的import语句
            const regex = /import\s+?(?:(?:(?:[\w*\s{},]*)\s+from\s+?)|)(?:(?:".*?")|(?:'.*?'))[\s]*?(?:;|$|)/g;
            let matches = content.match(regex);

            if (matches) {
                let log0 = ``;

                let needReplace = false;

                matches.forEach(match => {
                    // 获取绝对路径
                    const absolutePathRegex = /"@\/\/(.*?)"/;
                    const absolutePathMatch = match.match(absolutePathRegex);

                    if (absolutePathMatch) {
                        const absolutePath = absolutePathMatch[1].replace(/\\/g, '/');
                        let relativePath = path.relative(path.dirname(file), path.join(srcDirPath, absolutePath)).replace(/\\/g, '/');

                        if (relativePath.indexOf('/') === -1) {
                            relativePath = './'.concat(relativePath);
                        }

                        // 替换绝对路径为相对路径
                        content = content.replace(`"@//${absolutePath}"`, `"${relativePath}"`);

                        log0 = `- "@//${absolutePath}" 替换成 "${relativePath}"`;
                        needReplace = true;
                    }
                });

                if (needReplace) {
                    const filename = path.basename(file);
                    console.log('修改文件：' + filename + ' ' + file);
                    console.log(log0);
                }

                // 将修改后的内容写回文件
                fs.writeFileSync(file, content, 'utf-8');
            }
        }
    });
}

module.exports.replaceImportPaths = replaceImportPaths;