
const fs = require('fs');
// 反向读取模块 https://github.com/BaffinLee/readline-reverse https://github.com/icewizardry/readline-reverse
const ReadlineReverse = require('D:\\GITWarehouse\\Github\\readline-reverse');

/* *** 反向读取 spring boot 日志 ***
数组样例：
[
 '[2023-07-24 16:11:49 087] [INFO] [http-nio2-80-exec-59] [systemout] [2023-07-24 16:11:49.087 DEBUG 19105 --- [nio2-80-exec-59] c.c.s.m.e.m.E.getPagedListTotal          : ==> Parameters: jgw.cn(String), 28(String)',
  ']',
  "[2023-07-24 16:11:49 087] [INFO] [http-nio2-80-exec-59] [systemout] [2023-07-24 16:11:49.087 DEBUG 19105 --- [nio2-80-exec-59] c.c.s.m.e.m.E.getPagedListTotal          : ==>  Preparing: select count(*) rowCount from s_extension_app_data_officialdoc_sw tb_0 where tb_0.flow != -100 and tb_0.sac = ? and tb_0.app_id = ? and json_value(tb_0.search, '$.nb_sh_flow') in (0, -1) and (json_value(tb_0.search, '$.nb_czr') like '%20221207113257423-6d87cb%' or json_value(tb_0.search, '$.nb_hb_czr') like '%20221207113257423-6d87cb%') and json_value(tb_0.search, '$.nb_xgr_ids') like '%20221207113257423-6d87cb%' and json_value(tb_0.search, '$.nb_sh_flow') = 0",
  ']',
  '[2023-07-24 16:11:48 849] [INFO] [http-nio2-80-exec-53] [systemout] [2023-07-24 16:11:48.849 DEBUG 19105 --- [nio2-80-exec-53] c.c.s.m.e.m.E.getPagedList               : <==      Total: 3',
  ']'
]
*/
async function reverseReadSpringBootALogLines(filePath, startTime, endTime, limit) {
    const reader = new ReadlineReverse();
    let logs = [];

    await reader.open(filePath);
    while(true) {
        const lines = await reader.read(1);
        if (lines == null || lines.length == 0) {
            break;
        }

        const line = lines[0];
        if (line != null && line != '' && line != ']') {
            try {
                const logTimeStr = line.match(/\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) \d{3}\]/)[1];
                const logTime = Date.parse(logTimeStr);
        
                if (limit && logs.length >= limit) break;
                if (startTime && !(logTime >= Date.parse(startTime))) {
                    break;
                }
                if (endTime && !(logTime <= Date.parse(endTime))) {
                    break;
                }
        
                logs.push(line);
            } catch(exc) {
                console.log(exc.message);
                throw exc;
            }
        }
    }
    await reader.close();

    return logs;
    
    // return new Promise((resolve, reject) => {
    //     rev.on('line', (line) => {
    //         const logTime = new Date(line.split(' ')[0]); // 假设日志的时间戳在每行的开始
    //         if (startTime && logTime < new Date(startTime)) return resolve(logs);
    //         if (endTime && logTime > new Date(endTime)) return;
    //         logs.push(line);
    //         if (limit && logs.length >= limit) return resolve(logs);
    //     });
    //     rev.on('error', reject);
    //     rev.on('end', () => resolve(logs));
    // });
}

/* *** 正向读取 spring boot 日志 ***
数组样例：
[
 '[2023-07-24 16:11:49 087] [INFO] [http-nio2-80-exec-59] [systemout] [2023-07-24 16:11:49.087 DEBUG 19105 --- [nio2-80-exec-59] c.c.s.m.e.m.E.getPagedListTotal          : ==> Parameters: jgw.cn(String), 28(String)',
  ']',
  "[2023-07-24 16:11:49 087] [INFO] [http-nio2-80-exec-59] [systemout] [2023-07-24 16:11:49.087 DEBUG 19105 --- [nio2-80-exec-59] c.c.s.m.e.m.E.getPagedListTotal          : ==>  Preparing: select count(*) rowCount from s_extension_app_data_officialdoc_sw tb_0 where tb_0.flow != -100 and tb_0.sac = ? and tb_0.app_id = ? and json_value(tb_0.search, '$.nb_sh_flow') in (0, -1) and (json_value(tb_0.search, '$.nb_czr') like '%20221207113257423-6d87cb%' or json_value(tb_0.search, '$.nb_hb_czr') like '%20221207113257423-6d87cb%') and json_value(tb_0.search, '$.nb_xgr_ids') like '%20221207113257423-6d87cb%' and json_value(tb_0.search, '$.nb_sh_flow') = 0",
  ']',
  '[2023-07-24 16:11:48 849] [INFO] [http-nio2-80-exec-53] [systemout] [2023-07-24 16:11:48.849 DEBUG 19105 --- [nio2-80-exec-53] c.c.s.m.e.m.E.getPagedList               : <==      Total: 3',
  ']'
]
*/
async function readSpringBootALogLines(filePath, startTime, endTime, limit) {
    const reader = fs.createReadStream(filePath);
    let logs = [];

    let data = '';
    for await (const chunk of reader) {
        data += chunk;
        const lines = data.split('\n');
        data = lines.pop();

        for (const line of lines) {
            if (line != null && line != '' && line != ']') {
                try {
                    const logTimeStr = line.match(/\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) \d{3}\]/)[1];
                    const logTime = Date.parse(logTimeStr);
            
                    if (limit && logs.length >= limit) break;
                    if (startTime && !(logTime >= Date.parse(startTime))) {
                        break;
                    }
                    if (endTime && !(logTime <= Date.parse(endTime))) {
                        break;
                    }
            
                    logs.push(line);
                } catch(exc) {
                    console.log(exc.message);
                    throw exc;
                }
            }
        }

        if (limit && logs.length >= limit) break;
    }

    return logs;
}

module.exports.reverseReadSpringBootALogLines = reverseReadSpringBootALogLines;
module.exports.readSpringBootALogLines = readSpringBootALogLines;