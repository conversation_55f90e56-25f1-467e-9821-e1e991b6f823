@echo off
call bun build ./main.js --outfile=./dist/main.js --target=bun --minify
@REM call set path=E:\CSDevAppData\nodejs\node_global;D:\CSRuntime\nodejs16
@REM call webpack
@REM call webpack -c webpack.config.js

if not exist ".\dist" (
  mkdir ".\dist"
)

if not exist ".\dist\config" (
  mkdir ".\dist\config"
)

call copy /Y ".\package.json" ".\dist\package.json"
@REM call copy /Y ".\config\app.yml" ".\dist\config\app.yml"
call copy /Y ".\config\app-prod.yml" ".\dist\config\app-prod.yml"
@REM call xcopy /E /Y ".\config\*" ".\dist\config\"
call del ".\dist\main.js.LICENSE.txt"