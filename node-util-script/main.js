

const args = process.argv.slice(2);
console.log('启动参数共', args.length, '个');

function hasArg(v) {
    if (args != null && args.length > 0) {
        for (const i in args) {
            const arg = args[i];
            if (arg === v) {
                return true;
            }
        }
    }
    return false;
}

function getArgVal(n) {
    if (args != null && args.length > 0) {
        for (const i in args) {
            const arg = args[i];
            if (arg.startsWith(n + '=')) {
                return arg.substring(n.length + 1);
            }
        }
    }
}

/* C盘清理 */
// import DiskCClearService from './src/service/DiskCClearService.js';
// if (hasArg('dcc')) {
//     const dccs = new DiskCClearService();
//     dccs.start();
// }

/* 查找文件（文件名，文件内容） */
// import SearchFileService from './src/service/SearchFileService.js';
// if (hasArg('sf')) {
//     const kw = getArgVal('kw');
//     const path = getArgVal('path');
//     const matchTxtType = getArgVal('matchTxtType');
//     let options = {};
//
//     if (matchTxtType != null && matchTxtType.length > 0) options.matchTxtType = parseInt(matchTxtType);
//
//     console.log(`开始查找文件... ${kw} | ${path}`)
//     if (kw == null || kw.length === 0) console.error('关键词不能为空');
//     else if (path == null || path.length === 0) console.error('目标目录路径不能为空');
//     else {
//         const searchFileService = new SearchFileService();
//         const list = searchFileService.search(kw, path, options);
//         console.log(list);
//         // console.log(searchFileService.search('psf4', 'e:\\360MoveData\\Users\\Administrator\\Desktop\\临时'))
//     }
//     console.log(`查找文件完毕！`);
// }
//
// import BatchFileInDirService from './src/service/BatchFileInDirService.js';
// if (hasArg('bfd')) {
//     const bfd = new BatchFileInDirService();
//     bfd.start();
// }

/* 知识库数据修复 */
import {KbDataRepairService} from "./src/service/KbDataRepairService";
const kbDataRepairService = new KbDataRepairService({})
await kbDataRepairService.repair()

/* 解析日志 */
// import {LogReaderService} from "./src/service/LogReaderService";
// const lrs = new LogReaderService();
// lrs.filterLogToSave(`./data/default.log`, `./data/default_deleted.log`);


/* 生成知识库 */
// import { listFilePathsByWildcard } from "./src/util/dir_util";
// import { mergeMultiFilesToOneFile } from "./src/util/file_util";
// console.log('开始生成知识库...');
// const filePaths = listFilePathsByWildcard("D:\\GITWarehouse\\Other\\Framework\\csaui6jt\\csaui6jt-front\\doc", "【*】*");
// filePaths.splice(0, null, "D:\\GITWarehouse\\Other\\Framework\\csaui6jt\\csaui6jt-front\\src\\pages\\extension\\data\\ext-app-trigger-doc.js");
// mergeMultiFilesToOneFile(filePaths, 
//     "D:\\GITWarehouse\\自整理AI知识库\\csaui6jt-front\\csaui6jtkb.txt", 
//     // "D:\\GITWarehouse\\Other\\Tool\\code-util-script\\node-util-script\\data\\csaui6jtkb.txt", 
//     { splitStr: "\n\n" });
// console.log('生成知识库完毕！');

/* 动物园代码发布 */
// import { SFtpModel } from "./src/model/SFtpModel";
// import DwyOACodePublishService from "./src/service/DwyOACodePublishService";
// const docps = new DwyOACodePublishService();
// (async function() {
//     const sftpModel = new SFtpModel({
//         host: '**************',
//         port: 2200,
//         username: 'root@************_by_chengccz_sl',
//         password: 'Dianxin@12345!!'
//     });
//     console.log('加载文件列表1');
//     {
//         const fileList = await sftpModel.getRemoteFileListSync('/0002. ************ (XC_V_CT_SHSL_DWGL_APP_03)/root (own save)/tenv/data/sites/dwy-oa-back/web/open-web');
//         console.log(fileList);
//     }
//     console.log('加载文件列表2');
//     {
//         const fileList = await sftpModel.getRemoteFileListSync('/0002. ************ (XC_V_CT_SHSL_DWGL_APP_03)/root (own save)/tenv/data');
//         console.log(fileList);
//     }
//     const conn = await sftpModel.getConnSync();
//     conn.end();
    
//     // const list = await docps.getRemoteFileListSync('/0002. ************ (XC_V_CT_SHSL_DWGL_APP_03)/root (own save)/tenv/data/sites/dwy-oa-back/web/open-web');
//     // console.log(list);
//     // 上传后台war包
//     // await docps.uploadBackWarSync();
//     // 上传政务首页war包
//     // await docps.uploadInnerIndexWarSync();
// })();

/* 复制文件 */
// import CopyFileService from './src/service/CopyFileService.js';
// const cfs = new CopyFileService();
// cfs.searchFilesInDirAndCopyToDir('D:\\csruntime\\apache-tomcat-9.0.82\\webapps_lhzdzoa\\ROOT\\warehouse\\upload', 
// 'D:\\csruntime\\apache-tomcat-9.0.82\\webapps_lhzdzoa\\ROOT\\warehouse\\tmp', '.xlsx');

/* 文件查询 */
// const file_util = require('./src/util/file_util.js');
// // searchFilesInDirectory("E:\\CS\\Sites\\CZ20140604131312187_zjoa", "获得rootUrl");
// file_util.searchFilesInDirectory("D:\\GITWarehouse\\Other\\Tool\\code-util-script", "dst_file_path")
// console.log('执行完毕！')

/* 日志文件内容读取 */
// (async () => {
//     const log_read_util = require('./src/util/log_read_util.js');
    
//     // 反向读取
//     // const lines = await log_read_util.reverseReadSpringBootALogLines('../data/server.log', '2023-07-24 00:00:00', '2023-07-24 12:00:00', 3);
//     // const lines = await log_read_util.reverseReadSpringBootALogLines('../data/server.log', null, null, 3);

//     // 正向读取
//     // const lines = await log_read_util.readSpringBootALogLines('../data/server.log', '2023-07-24 00:00:00', '2023-07-24 12:00:00', 3);
//     const lines = await log_read_util.readSpringBootALogLines('../data/server.log', null, null, 20);

//     // 输出
//     console.log(lines)
//     console.log('执行完毕！')
// })()

/* 替换src目录中代码的import路径 */
// (async () => {
//     const code_import_replace = require('./src/util/code_import_replace.js');

//     code_import_replace.replaceImportPaths('D:\\GITWarehouse\\Other\\Prototype\\csjsv6-threejs-prototype\\src', 
//     ['api/core', 'api/my-apps/demo-2d-photo']);
// })()