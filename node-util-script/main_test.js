
const fs = require('fs');
const path = require('path');

let dirPath = 'C:\\data\\workspace\\pjt-prototype\\csaui6jt-front\\dist\\js';

// 查找目录中JS文件里的所有lodash版本号
function findAllLodashVersions(directory) {
    const results = [];
    
    try {
        const files = fs.readdirSync(directory);
        const jsFiles = files.filter(file => file.endsWith('.js'));
        
        console.log(`在目录 ${directory} 中找到 ${jsFiles.length} 个JS文件`);
        
        for (const file of jsFiles) {
            const filePath = path.join(directory, file);
            try {
                const content = fs.readFileSync(filePath, 'utf8');
                
                // 查找lodash版本信息的正则表达式
                const versionPatterns = [
                    /lodash[\s\S]*?version[\s\S]*?["']([\d\.]+)["']/i,
                    /["']lodash["'][\s\S]*?["']([\d\.]+)["']/i,
                    /@version\s+([\d\.]+)/i,
                    /version:\s*["']([\d\.]+)["']/i,
                    /lodash\s+v?([\d\.]+)/i
                ];
                
                for (const pattern of versionPatterns) {
                    const match = content.match(pattern);
                    if (match) {
                        const result = {
                            fileName: file,
                            version: match[1],
                            matchedText: match[0].substring(0, 100) // 显示匹配的前100个字符
                        };
                        results.push(result);
                        console.log(`在文件 ${file} 中找到lodash版本: ${match[1]}`);
                        console.log(`匹配内容: ${match[0].substring(0, 100)}...`);
                        break; // 找到版本后跳出当前文件的匹配循环
                    }
                }
                
                // 如果没找到版本号，但文件包含lodash，记录一下
                if (content.includes('lodash') && !results.some(r => r.fileName === file)) {
                    console.log(`文件 ${file} 包含lodash但未找到版本号`);
                }
                
            } catch (err) {
                console.log(`读取文件 ${file} 时出错: ${err.message}`);
            }
        }
        
        return results;
        
    } catch (err) {
        console.error(`读取目录时出错: ${err.message}`);
        return [];
    }
}

// 执行查找
console.log('开始查找所有lodash版本号...');
const results = findAllLodashVersions(dirPath);

if (results.length > 0) {
    console.log(`\n找到 ${results.length} 个包含lodash版本信息的文件:`);
    console.log('========================================');
    results.forEach((result, index) => {
        console.log(`${index + 1}. 文件: ${result.fileName}`);
        console.log(`   版本: ${result.version}`);
        console.log(`   匹配内容: ${result.matchedText}...`);
        console.log('----------------------------------------');
    });
} else {
    console.log('\n未找到任何lodash版本信息');
}