
const fs = require('fs');
const path = require('path');

// 配置参数
let dirPath = 'C:\\data\\game\\Steam\\steamapps\\common\\Astrox Imperium';
let searchString = 'Civil'; // 要搜索的字符串
let fileExtensions = ['.js', '.ts', '.jsx', '.tsx', '.vue', '.html', '.css', '.json', '.txt']; // 支持的文件扩展名
let ignoreExtensions = [];
let contextLength = 20; // 显示匹配内容前后的字符数
let ignoreCase = true; // 是否忽略大小写（true=忽略，false=区分）

// 查找目录中文件里包含指定字符串的内容
function findStringInFiles(directory, searchStr, extensions = ['.js'], caseSensitive = false) {
    const results = [];
    try {
        const files = fs.readdirSync(directory);
        for (const file of files) {
            const filePath = path.join(directory, file);
            const stat = fs.statSync(filePath);
            if (stat.isDirectory()) {
                // 递归处理子目录
                const subDirResults = findStringInFiles(filePath, searchStr, extensions, caseSensitive);
                results.push(...subDirResults);
            } else if (extensions.some(ext => file.toLowerCase().endsWith(ext.toLowerCase()))) {
                try {
                    const content = fs.readFileSync(filePath, 'utf8');
                    const lines = content.split('\n');
                    let fileResults = [];
                    lines.forEach((line, lineIndex) => {
                        let isMatch = false;
                        let startIndex = -1;
                        if (caseSensitive) {
                            isMatch = line.includes(searchStr);
                            startIndex = line.indexOf(searchStr);
                        } else {
                            isMatch = line.toLowerCase().includes(searchStr.toLowerCase());
                            startIndex = line.toLowerCase().indexOf(searchStr.toLowerCase());
                        }
                        if (isMatch) {
                            const contextStart = Math.max(0, startIndex - contextLength);
                            const contextEnd = Math.min(line.length, startIndex + searchStr.length + contextLength);
                            const matchContent = line.substring(contextStart, contextEnd);
                            fileResults.push({
                                fileName: filePath,
                                matchContent: matchContent
                            });
                        }
                    });
                    if (fileResults.length > 0) {
                        results.push(...fileResults);
                    }
                } catch (err) {
                    console.log(`读取文件 ${file} 时出错: ${err.message}`);
                }
            }
        }
        return results;
    } catch (err) {
        console.error(`读取目录时出错: ${err.message}`);
        return [];
    }
}

// 执行查找
console.log(`开始在目录中查找包含 "${searchString}" 的文件...`);
console.log(`大小写敏感: ${!ignoreCase ? '是' : '否'}`);
const results = findStringInFiles(dirPath, searchString, fileExtensions, !ignoreCase);

if (results.length > 0) {
    console.log(`\n找到 ${results.length} 处匹配:`);
    console.log('========================================');
    results.forEach((result, index) => {
        console.log(`${index + 1}. 文件: ${result.fileName}`);
        console.log(`   匹配内容: ${result.matchContent}`);
        console.log('----------------------------------------');
    });

    console.log(`\n总计: 找到 ${results.length} 处匹配`);

    // // 返回结果列表
    // console.log('\n结果列表:');
    // console.log(JSON.stringify(results, null, 2));
} else {
    console.log(`\n未找到任何包含 "${searchString}" 的内容`);
}

console.log(`\n\n\n`)